# Единая система авторизации через токены

## Обзор

Реализована единая система авторизации между `fmadmin` (админк<PERSON>) и `mainweb` (основной сайт) через токены, хранящиеся в базе данных и cookies.

## Принцип работы

### 1. **Авторизация пользователя**
- Пользователь вводит email/пароль в любом из приложений
- Система генерирует уникальный токен с timestamp
- Токен хешируется и сохраняется в поле `token` таблицы `users`
- Токен устанавливается в httpOnly cookie с TTL 30 дней

### 2. **Проверка авторизации**
- При каждом запросе проверяется токен из cookies
- Токен валидируется по БД и проверяется срок действия
- При валидном токене пользователь автоматически авторизуется
- Обновляется время последней активности (`last_online`)

### 3. **Выход из системы**
- Токен удаляется из БД
- Cookie очищается
- Пользователь выходит из всех приложений одновременно

## Структура токена

```
token_format = "random_string:timestamp"
stored_token = sha256(token_format)
```

**Пример:**
```
Исходный токен: "abc123def456:1640995200"
Хешированный: "a1b2c3d4e5f6..."
```

## Компоненты системы

### 1. **shared_auth.py** - Основной модуль
```python
from shared_auth import get_auth_manager

auth_manager = get_auth_manager()

# Авторизация
user, token = auth_manager.login_user(email, password)

# Проверка токена
user = auth_manager.validate_token(token)

# Выход
auth_manager.invalidate_token(user_id)
```

### 2. **Обновленные декораторы**

#### mainweb/run.py
```python
@login_required
def protected_route():
    # Автоматически проверяет токен из cookies
    # Обновляет сессию при валидном токене
    pass
```

#### fmadmin/run.py
```python
@is_allowed
def admin_route():
    # Проверяет токен + права администратора
    # Работает для admin и editor ролей
    pass
```

### 3. **Обновленные функции входа/выхода**

#### Вход (оба приложения)
```python
@app.route('/login', methods=['POST'])
def login():
    user, token = auth_manager.login_user(email, password)
    if user and token:
        response = redirect(url_for('dashboard'))
        response.set_cookie('auth_token', token, 
                          max_age=30*24*60*60, httponly=True)
        return response
```

#### Выход (оба приложения)
```python
@app.route('/logout')
def logout():
    user_id = session.get('user_id')
    if user_id:
        auth_manager.invalidate_token(user_id)
    
    response = redirect(url_for('index'))
    response.set_cookie('auth_token', '', expires=0)
    return response
```

## Безопасность

### ✅ Реализованные меры
- **HttpOnly cookies** - защита от XSS атак
- **Хеширование токенов** - токены не хранятся в открытом виде
- **TTL токенов** - автоматическое истечение через 30 дней
- **Проверка ролей** - разграничение доступа admin/editor/user
- **Проверка блокировки** - заблокированные пользователи не авторизуются

### 🔄 Рекомендации для production
- **HTTPS only** - установить `secure=True` для cookies
- **CSRF защита** - добавить CSRF токены для форм
- **Rate limiting** - ограничение попыток входа
- **Логирование** - запись попыток авторизации

## Использование

### Авторизация в mainweb
1. Пользователь заходит на сайт и авторизуется
2. Получает токен в cookies
3. Может переходить в админку без повторной авторизации (если есть права)

### Авторизация в fmadmin
1. Администратор заходит в админку
2. Получает токен в cookies
3. Может переходить на основной сайт без повторной авторизации

### Кнопка "Админ панель"
- Отображается только для пользователей с ролями `admin` или `editor`
- Находится в header основного сайта
- Открывает админку в новой вкладке

## API SharedAuthManager

### Методы класса

```python
class SharedAuthManager:
    def __init__(self, db_config=None)
    def generate_auth_token(self, user_id) -> str
    def validate_token(self, token) -> dict|None
    def invalidate_token(self, user_id) -> None
    def login_user(self, email, password) -> tuple[dict|None, str|None]
    def get_user_by_id(self, user_id) -> dict|None
    def check_admin_access(self, user) -> bool
    def check_user_access(self, user) -> bool
```

### Быстрые функции

```python
# Импорт
from shared_auth import login_user, validate_token, invalidate_token

# Использование
user, token = login_user(email, password)
user = validate_token(token)
invalidate_token(user_id)
```

## Миграция с старой системы

### Совместимость
- Старые сессии продолжают работать
- Постепенный переход на токены при новых входах
- Декораторы проверяют и токены, и сессии

### Обновление существующих пользователей
```python
# При первом входе после обновления
# автоматически генерируется токен
```

## Тестирование

### Запуск тестов модуля
```bash
python shared_auth.py
```

### Проверка работы
1. Авторизуйтесь в mainweb
2. Перейдите по ссылке "Админ панель" (если есть права)
3. Убедитесь, что вход в админку произошел автоматически
4. Выйдите из любого приложения
5. Убедитесь, что вышли из обоих приложений

## Troubleshooting

### Токен не работает
1. Проверьте настройки cookies (httpOnly, domain)
2. Убедитесь, что время сервера синхронизировано
3. Проверьте TTL токена (30 дней по умолчанию)

### Не работает переход между приложениями
1. Убедитесь, что оба приложения используют одну БД
2. Проверьте, что shared_auth.py доступен обоим приложениям
3. Проверьте настройки cookies (domain, path)

### Ошибки авторизации
1. Проверьте подключение к БД
2. Убедитесь, что поле `token` существует в таблице `users`
3. Проверьте права пользователя (admin/editor для fmadmin)

## Конфигурация

### Настройки по умолчанию
```python
TOKEN_TTL = 30 * 24 * 60 * 60  # 30 дней
COOKIE_MAX_AGE = 30 * 24 * 60 * 60  # 30 дней
COOKIE_HTTPONLY = True
COOKIE_SECURE = False  # True для production
COOKIE_SAMESITE = 'Lax'
```

### Изменение настроек
```python
# В shared_auth.py
auth_manager.token_ttl = 7 * 24 * 60 * 60  # 7 дней

# В приложениях при установке cookie
response.set_cookie('auth_token', token, 
                   max_age=7*24*60*60,  # 7 дней
                   secure=True)  # Для HTTPS
```

## Логи и мониторинг

### Рекомендуемые метрики
- Количество успешных авторизаций
- Количество неудачных попыток входа
- Время жизни токенов
- Количество активных сессий

### Логирование
```python
import logging

# В shared_auth.py добавить логирование
logging.info(f"User {user_id} logged in")
logging.warning(f"Invalid token attempt: {token[:10]}...")
logging.info(f"User {user_id} logged out")
```
