# Система переводов через базу данных

## Обзор изменений

Система переводов была обновлена для работы через единую базу данных вместо статических файлов. Теперь оба приложения (fmadmin и mainweb) используют одну таблицу `translations` в PostgreSQL.

## Преимущества новой системы

✅ **Единый источник данных** - переводы хранятся в БД, доступной обоим приложениям
✅ **Динамическое обновление** - изменения в админке сразу доступны на сайте
✅ **Кеширование** - переводы кешируются на 5 минут для производительности
✅ **Fallback система** - при недоступности БД используются базовые переводы
✅ **API для управления** - возможность очистки кеша через API

## Структура таблицы translations

```sql
CREATE TABLE translations (
    id SERIAL PRIMARY KEY,
    alias VARCHAR(255) UNIQUE NOT NULL,
    content TEXT,           -- Английский (основной)
    content_ru TEXT,        -- Русский
    content_uz TEXT,        -- Узбекский
    created_at BIGINT
);
```

## Миграция статических переводов

### 1. Запуск миграции

```bash
python migrate_translations.py
```

Этот скрипт:
- Читает статические переводы из `mainweb/modules/translations.py`
- Переносит их в таблицу `translations`
- Обновляет существующие записи при необходимости
- Выводит подробную статистику

### 2. Проверка миграции

Скрипт автоматически проверяет корректность миграции и выводит статистику:

```
📈 СТАТИСТИКА МИГРАЦИИ:
➕ Добавлено новых переводов: 45
🔄 Обновлено существующих: 3
⏭️ Пропущено (без изменений): 12
📊 Всего обработано ключей: 60
```

## Синхронизация между приложениями

### Автоматическая синхронизация

После изменений переводов в fmadmin используйте:

```bash
python sync_translations.py
```

Или с указанием URL mainweb:

```bash
python sync_translations.py http://localhost:16534
```

### Ручная очистка кеша

Через API mainweb:

```bash
curl -X POST http://localhost:16534/api/translations/clear_cache
```

## Использование в коде

### В mainweb

Функция `t()` автоматически загружает переводы из БД:

```python
from modules.translate import t

# В шаблонах
{{ t('website_title') }}
{{ t('login') }}

# В Python коде
title = t('website_title')
```

### В fmadmin

Переводы работают как раньше через таблицу БД:

```python
# Добавление нового перевода
dbc.translations.add(
    alias='new_key',
    content='English text',
    content_ru='Русский текст',
    content_uz='O\'zbek matni'
).exec()

# Обновление перевода
dbc.translations.all().equal(alias='existing_key').update(
    content='Updated English',
    content_ru='Обновленный русский'
).exec()
```

## Кеширование

### Настройки кеша

- **TTL**: 5 минут (300 секунд)
- **Автообновление**: при истечении TTL
- **Fallback**: базовые переводы при ошибке БД

### Принудительная очистка

```python
from modules.translate import clear_translations_cache
clear_translations_cache()
```

## Fallback система

При недоступности БД используются базовые переводы:

```python
fallback_translations = {
    'en': {
        'website_title': 'Philology Matters',
        'login': 'Login',
        'register': 'Register',
        # ...
    },
    'ru': {
        'website_title': 'Филологические задачи',
        'login': 'Вход',
        'register': 'Регистрация',
        # ...
    },
    'uz': {
        'website_title': 'Filologiya masalalari',
        'login': 'Kirish',
        'register': 'Ro\'yxatdan o\'tish',
        # ...
    }
}
```

## Мониторинг и отладка

### Проверка состояния переводов

```bash
python sync_translations.py
```

Выводит:
- Количество переводов в БД
- Проверку ключевых переводов
- Статистику пустых переводов

### Логирование

Ошибки загрузки переводов выводятся в консоль:

```
Ошибка загрузки переводов из БД: connection refused
```

## Рекомендации

### Для разработки

1. **После изменений в fmadmin**: запустите `sync_translations.py`
2. **При добавлении новых ключей**: обновите fallback переводы
3. **При проблемах с кешем**: используйте API очистки кеша

### Для production

1. **Настройте мониторинг** доступности БД
2. **Увеличьте TTL кеша** до 15-30 минут
3. **Добавьте логирование** в файлы
4. **Настройте автоматическую синхронизацию** через cron

## Troubleshooting

### Переводы не обновляются

1. Проверьте подключение к БД
2. Очистите кеш: `curl -X POST http://localhost:16534/api/translations/clear_cache`
3. Перезапустите mainweb

### Ошибки подключения к БД

1. Проверьте настройки в `config.py`
2. Убедитесь, что PostgreSQL запущен
3. Проверьте права доступа к БД

### Отсутствуют переводы

1. Запустите миграцию: `python migrate_translations.py`
2. Проверьте таблицу `translations` в БД
3. Добавьте недостающие переводы через fmadmin

## API Reference

### POST /api/translations/clear_cache

Очищает кеш переводов в mainweb.

**Response:**
```json
{
    "success": true,
    "message": "Translations cache cleared successfully"
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Error clearing cache: connection refused"
}
```
