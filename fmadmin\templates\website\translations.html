{%  extends 'basic.html' %}


{% block content %}

<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">Переводы</h2>
        </div>
        <div class="col-auto ms-auto d-print-none">
            <button type="button" class="btn btn-primary" id="sync-translations-btn">
                <i class="ti ti-refresh"></i>
                Синхронизировать с mainweb
            </button>
        </div>
    </div>
</div>
<form method="get" action="/fmadmin/website/translations" class="mb-3">
  <div class="input-group">
    <input type="text" class="form-control" name="search" placeholder="Поиск по тексту или алиасу..." value="{{ search|default('') }}">
    <button class="btn btn-primary" type="submit"><i class="ti ti-search"></i> Найти</button>
  </div>
</form>
<div class="card mt-4">
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>Алиас</th>
          <th>Узбекский</th>
          <th>Русский</th>
          <th>Английский</th>
          <th class="w-1"></th>
        </tr>
      </thead>
      <tbody>
        {% for t in translations %}
        <tr>
          <td class="align-middle">{{ t.alias }}</td>
          <td class="align-middle">{% if t.content_uz%}{{ t.content_uz[:120] }}{% if t.content_uz|length > 120 %}...{% endif %}{% else %}-{% endif %}</td>
          <td class="align-middle">{% if t.content_ru%}{{ t.content_ru[:120] }}{% if t.content_ru|length > 120 %}...{% endif %}{% else %}-{% endif %}</td>
          <td class="align-middle">{% if t.content%}{{ t.content[:120] }}{% if t.content|length > 120 %}...{% endif %}{% else %}-{% endif %}</td>
          <td class="align-middle">
            <a href="#" class="btn btn-icon btn-outline-primary bg-primary-lt btn-sm px-2 py-1" data-bs-toggle="modal" data-bs-target="#modal-edit-translation" data-alias="{{ t.alias }}">
              Редактировать
            </a>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<div class="modal modal-blur fade" id="modal-edit-translation" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Редактирование перевода</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label class="form-label">Алиас</label>
          <input type="text" class="form-control" id="edit-alias" readonly>
        </div>
        <div class="mb-3">
          <label class="form-label">Узбекский</label>
          <input type="text" class="form-control" id="edit-content-uz">
        </div>
        <div class="mb-3">
          <label class="form-label">Русский</label>
          <input type="text" class="form-control" id="edit-content-ru">
        </div>
        <div class="mb-3">
          <label class="form-label">Английский</label>
          <input type="text" class="form-control" id="edit-content">
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link link-secondary" data-bs-dismiss="modal">
          Отмена
        </button>
        <button type="button" class="btn btn-primary ms-auto" id="save-translation-btn">
          Сохранить
        </button>
      </div>
    </div>
  </div>
</div>


{% endblock %}

{% block scripts %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const editModal = document.getElementById('modal-edit-translation');
    let currentAlias = null;

    // Обработка модального окна редактирования
    editModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const alias = button.getAttribute('data-alias');
        currentAlias = alias;
        fetch(`/fmadmin/api/translation/${alias}`)
          .then(r => r.json())
          .then(data => {
            document.getElementById('edit-alias').value = data.alias || '';
            document.getElementById('edit-content-uz').value = data.content_uz || '';
            document.getElementById('edit-content-ru').value = data.content_ru || '';
            document.getElementById('edit-content').value = data.content || '';
          });
    });

    // Сохранение перевода
    document.getElementById('save-translation-btn').onclick = function() {
        fetch(`/fmadmin/api/translation/${currentAlias}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                content_uz: document.getElementById('edit-content-uz').value,
                content_ru: document.getElementById('edit-content-ru').value,
                content: document.getElementById('edit-content').value
            })
        }).then(r => r.json()).then(data => {
            if (data.success) {
                location.reload();
            }
        });
    };

    // Синхронизация переводов с mainweb
    document.getElementById('sync-translations-btn').onclick = function() {
        const btn = this;
        const originalText = btn.innerHTML;

        // Показываем индикатор загрузки
        btn.innerHTML = '<i class="ti ti-loader-2 ti-spin"></i> Синхронизация...';
        btn.disabled = true;

        fetch('/fmadmin/api/sync-translations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(r => r.json())
        .then(data => {
            if (data.success) {
                // Показываем успешное сообщение
                btn.innerHTML = '<i class="ti ti-check"></i> Синхронизировано!';
                btn.className = 'btn btn-success';

                // Показываем toast уведомление
                showToast('Переводы успешно синхронизированы с mainweb', 'success');

                // Возвращаем кнопку в исходное состояние через 3 секунды
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.className = 'btn btn-primary';
                    btn.disabled = false;
                }, 3000);
            } else {
                // Показываем ошибку
                btn.innerHTML = '<i class="ti ti-x"></i> Ошибка';
                btn.className = 'btn btn-danger';

                showToast(data.message || 'Ошибка синхронизации', 'error');

                // Возвращаем кнопку в исходное состояние через 3 секунды
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.className = 'btn btn-primary';
                    btn.disabled = false;
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Sync error:', error);
            btn.innerHTML = '<i class="ti ti-x"></i> Ошибка';
            btn.className = 'btn btn-danger';

            showToast('Ошибка подключения к серверу', 'error');

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.className = 'btn btn-primary';
                btn.disabled = false;
            }, 3000);
        });
    };
});

// Функция для показа toast уведомлений
function showToast(message, type = 'info') {
    // Создаем toast элемент
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Автоматически удаляем через 5 секунд
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>


{% endblock %}