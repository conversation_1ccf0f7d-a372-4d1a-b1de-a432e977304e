import time
import json
from flask import Flask, render_template, session, request, jsonify, flash, redirect, url_for, send_file
from functools import wraps
import os
import sys
from werkzeug.utils import secure_filename
from modules.connector import PostgreSQLConnector
from modules.translate import t, translate, clear_translations_cache
from config import *

# Добавляем путь к shared_auth модулю
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_auth import get_auth_manager

app = Flask(__name__)

app.secret_key = 'd2c9cb10-0d85-432d-8c01-d9b0303dedb8'
dbc = PostgreSQLConnector(host=DB_HOST, port=DB_PORT, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)
auth_manager = get_auth_manager()

# Authentication decorators
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Проверяем токен в cookies
        token = request.cookies.get('auth_token')
        if token:
            user = auth_manager.validate_token(token)
            if user:
                # Обновляем сессию
                session['user'] = user
                session['user_id'] = user['id']
                return f(*args, **kwargs)

        # Проверяем старую сессию (для совместимости)
        if 'user_id' not in session:
            flash('Please log in to access this page', 'error')
            return redirect(url_for('app__login'))
        return f(*args, **kwargs)
    return decorated_function

def not_auth_only(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Проверяем токен в cookies
        token = request.cookies.get('auth_token')
        if token:
            user = auth_manager.validate_token(token)
            if user:
                return redirect(url_for('app__dashboard'))

        # Проверяем старую сессию (для совместимости)
        if 'user_id' in session:
            return redirect(url_for('app__dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Configure upload folder
UPLOAD_FOLDER = 'static/uploads'
AVATARS_FOLDER = os.path.join(UPLOAD_FOLDER, 'avatars')
ARTICLES_FOLDER = os.path.join(UPLOAD_FOLDER, 'articles')
DOCUMENTS_FOLDER = os.path.join(UPLOAD_FOLDER, 'documents')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['AVATARS_FOLDER'] = AVATARS_FOLDER
app.config['ARTICLES_FOLDER'] = ARTICLES_FOLDER
app.config['DOCUMENTS_FOLDER'] = DOCUMENTS_FOLDER

# Ensure upload directories exist
os.makedirs(app.config['AVATARS_FOLDER'], exist_ok=True)
os.makedirs(app.config['ARTICLES_FOLDER'], exist_ok=True)
os.makedirs(app.config['DOCUMENTS_FOLDER'], exist_ok=True)

def allowed_file(filename, extensions=ALLOWED_EXTENSIONS):
    print(filename.rsplit('.', 1)[1].lower())
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in extensions

@app.template_filter('timestamp_to_date')
def timestamp_to_date(timestamp):
    if not timestamp:
        return ''
    return time.strftime('%d.%m.%Y', time.localtime(timestamp))

@app.template_filter('status_color')
def status_color(status):
    colors = {
        'declined': 'red',
        'unpaid': 'blue',
        'paid': 'green',
        'pending': 'blue'
    }
    return colors.get(status, 'gray')

@app.template_filter('status_text')
def status_text(status):
    texts = {
        'declined': 'Declined',
        'unpaid': 'Waiting payment',
        'paid': 'Activated',
        'pending': 'Under review'
    }
    return texts.get(status, status.title())

@app.template_filter('format_currency')
def format_currency(value, currency='usd'):
    """Format currency with proper thousands separators"""
    try:
        value = float(value)
        if currency == 'uzs':
            return f"{value:,.0f} UZS"
        elif currency == 'rub':
            return f"{value:,.0f} ₽"
        else:
            return f"${value:,.2f}"
    except (ValueError, TypeError):
        return str(value)

@app.route("/")
def app__index():
    # Public route - no auth decorator needed
    latest_publications = dbc.publications.get().order_by('date_publish').per_page(8).page(1).exec()
    downloaded_publications = dbc.publications.get().order_by('date_publish').per_page(8).page(1).exec()
    popular_publications = dbc.publications.get().order_by('stat_views').per_page(8).page(1).exec()
    
    # Enhance all publications with author information
    for publications in [latest_publications, downloaded_publications, popular_publications]:
        for pub in publications:
            # Get main author
            if pub['main_author_id']:
                main_author = dbc.author_profile.get(id=pub['main_author_id']).exec()
                if main_author:
                    pub['main_author_name'] = main_author[0]['name']
            
            # Get co-authors
            pub['subauthor_names'] = []
            if pub['subauthor_ids']:
                for author_id in pub['subauthor_ids']:
                    co_author = dbc.author_profile.get(id=author_id).exec()
                    if co_author:
                        pub['subauthor_names'].append(co_author[0]['name'])
            
            # Count citations
            pub['citations_count'] = len(dbc.publication_citations.get(publication_id=pub['id']).exec())
            pub['references_count'] = len(dbc.publication_refs.get(publication_id=pub['id']).exec())
    
    # Get news and announcements
    news_items = dbc.news.get(type='news', status='published').order_by('published_at').per_page(2).page(1).exec()
    announcements = dbc.news.get(type='announcement', status='published').order_by('published_at').per_page(2).page(1).exec()
    
    # Apply translations to news items
    for item in news_items + announcements:
        item = translate(item)

    return render_template('index.html',
                         latest_publications=latest_publications,
                         downloaded_publications=downloaded_publications,
                         popular_publications=popular_publications,
                         news_items=news_items,
                         announcements=announcements)

@app.route("/editorial")
def app__editorial():
    editors = [
        {'title': 'text',
        'full_name': 'text',
        'organization': 'text',
        'biography': '',}
    ]
    return render_template('mainweb/editorial.html', editors = editors)

@app.route("/page/<string:alias>")
def app__page_alias(alias):
    _page = dbc.pages.get(alias=alias).exec()
    if not _page:
        return redirect(url_for('app__index'))
    page = _page[0]
    
    # Define page groups based on their aliases
    page_groups = {
        'submission': ['submission_guidelines', 'author_instructions', 'editorial_policy', 'site_editing_services', 'video_guides'],
        'about_journal': ['journal_metrics', 'aims_scope', 'journal_info', 'news_calls'],
        'subscribe': ['for_uzgumya_researchers', 'for_all_researchers', "masters_subscription"]
    }
    
    # Find which group the current page belongs to
    current_group = None
    for group, aliases in page_groups.items():
        if alias in aliases:
            current_group = group
            break
    
    # Get all pages
    all_pages = dbc.pages.get().exec()
    
    # Filter related pages based on the current page's group
    sidebar_pages = []
    if current_group:
        for p in all_pages:
            if p['alias'] in page_groups[current_group]:  # Removed the exclusion of current page
                sidebar_pages.append({
                    'url': url_for('app__page_alias', alias=p['alias']),
                    'title': p['title'],
                    'active': p['alias'] == alias
                })
    
    return render_template('mainweb/page.html', 
                         page=page,
                         related_pages=sidebar_pages)

@app.route("/login", methods=['GET', 'POST'])
@not_auth_only
def app__login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        # Используем новую систему авторизации через токены
        user, token = auth_manager.login_user(email, password)

        if user and token:
            # Сохраняем в сессии для совместимости
            session['user'] = user
            session['user_id'] = user['id']

            # Создаем ответ с редиректом
            response = redirect(url_for('app__index'))

            # Устанавливаем токен в cookies (httponly для безопасности)
            response.set_cookie(
                'auth_token',
                token,
                max_age=30*24*60*60,  # 30 дней
                httponly=True,
                secure=False,  # В production должно быть True для HTTPS
                samesite='Lax'
            )

            flash(f'Welcome, {user["name"]}!', 'success')
            return response

        flash('Invalid login or password. Try again.', 'error')
        return redirect(url_for('app__login'))

    return render_template('auth/login.html')

@app.route("/register", methods=['GET', 'POST'])
@not_auth_only
def app__register():
    if request.method == 'POST':
    
        email = request.form.get('email')
        email_confirm = request.form.get('email_confirm')

        password = request.form.get('password')
        password_confirm = request.form.get('password_confirm')

        agree_terms = request.form.get('agree_terms', '0') == '1'

        if not agree_terms:
            flash('You must agree to the terms and conditions', 'error')

        elif email != email_confirm:
            flash('Emails do not match', 'error')
        
        elif password != password_confirm:
            flash('Passwords do not match', 'error')
        else:
            existing_user = dbc.users.get(email=email).exec()
            
            if existing_user:
                flash('Email already registered', 'error')
                return redirect(url_for('app__login'))

            data = {
                'name': request.form.get('first_name'),
                'second_name': request.form.get('last_name'),
                'father_name': request.form.get('surname'),
                'country_id': request.form.get('country'),
                'region': request.form.get('region'),
                'email': request.form.get('email'),
                'password': request.form.get('password'),
                'rolename': 'user',
                'is_blocked': False,
                'is_notify': request.form.get('is_notify', '0') == '1',
                'accept_rules_time': int(time.time()),
                'last_online': int(time.time()),
            }

            _res = dbc.users.add(**data).exec()
            if _res:
                session['user'] = _res[0]
                session['user_id'] = _res[0]['id']
                return redirect(url_for('app__index'))
    
    fix_country = dbc.fix_country.get().exec()
    
    return render_template('auth/register.html', fix_country = fix_country)

@app.route("/logout")
@login_required
def app__logout():
    # Получаем user_id для аннулирования токена
    user_id = session.get('user_id')

    # Аннулируем токен в БД
    if user_id:
        auth_manager.invalidate_token(user_id)

    # Очищаем сессию
    session.pop('user', None)
    session.pop('user_id', None)

    # Создаем ответ с редиректом
    response = redirect(url_for('app__index'))

    # Удаляем токен из cookies
    response.set_cookie('auth_token', '', expires=0)

    flash('Logged out.', 'info')
    return response

@app.route("/dashboard")
@login_required
def app__dashboard():
    return render_template('dashboard/index.html')

@app.route("/contact", methods=['GET', 'POST'])
def app__contact():
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        subject = request.form.get('subject')
        message = request.form.get('message')
        
        # Here you would typically send an email
        # For now, we'll just flash a success message
        flash('message_sent', 'success')
        return redirect(url_for('app__contact'))
        
    return render_template('mainweb/contact.html')

@app.route("/dashboard/articles")
@login_required
def app__dashboard_articles():
    # Get user's submissions
    submissions = dbc.submissions.get(user_id=session['user_id']).order_by('created_date').exec()
    
    # Apply translation to submissions
    for submission in submissions:
        submission = translate(submission)
    
    # Get author profiles for the submissions
    author_profiles = {}
    if submissions:
        for submission in submissions:
            # Get main author profile
            if submission['main_author_id']:
                main_author = dbc.author_profile.get(id=submission['main_author_id']).exec()
                if main_author:
                    author_profiles[submission['main_author_id']] = translate(main_author[0])
            
            # Get co-authors profiles
            if submission['sub_author_ids']:
                for author_id in submission['sub_author_ids']:
                    if author_id not in author_profiles:
                        co_author = dbc.author_profile.get(id=author_id).exec()
                        if co_author:
                            author_profiles[author_id] = translate(co_author[0])
    
    return render_template('dashboard/articles.html', 
                         submissions=submissions,
                         author_profiles=author_profiles)

@app.route("/dashboard/articles/delete/<int:submission_id>", methods=['POST'])
@login_required
def app__dashboard_articles_delete(submission_id):
    # Get the submission
    submission = dbc.submissions.get(id=submission_id, user_id=session['user_id']).exec()
    if not submission:
        return jsonify({'success': False, 'message': 'Submission not found'})
    
    # Only allow deletion of drafts
    if submission[0]['status'] != 'draft':
        return jsonify({'success': False, 'message': 'Only draft submissions can be deleted'})
    
    # Delete the submission
    dbc.submissions.get(id=submission_id, user_id=session['user_id']).delete().exec()
    
    return jsonify({'success': True})

@app.route("/dashboard/purchases")
@login_required
def app__dashboard_purchases():
    # Get user's payments
    payments = dbc.payments.get(user_id=session['user_id']).order_by('created_at').exec()
    
    # Get user data for subscription end date
    user = dbc.users.get(id=session['user_id']).exec()[0]
    
    subscription_active = False
    subscription_end_date = None
    days_left = None
    
    if user.get('subscription_end_date'):
        current_time = int(time.time())
        subscription_end_date = user['subscription_end_date']
        
        if subscription_end_date > current_time:
            subscription_active = True
            days_left = (subscription_end_date - current_time) // (24 * 60 * 60)
    
    # For article purchases, get article details
    for payment in payments:
        if payment['payment_type'] == 'article' and payment['ids']:
            # Get article details for each ID
            articles = []
            for article_id in payment['ids']:
                article = dbc.submissions.get(id=article_id).exec()
                if article:
                    # Apply translation to article data
                    translated_article = translate(article[0])
                    
                    # Get main author
                    main_author = None
                    if translated_article['main_author_id']:
                        author = dbc.author_profile.get(id=translated_article['main_author_id']).exec()
                        if author:
                            main_author = translate(author[0])
                    
                    articles.append({
                        'id': translated_article['id'],
                        'title': translated_article['title'],
                        'author': main_author['name'] if main_author else 'Unknown',
                        'volume': translated_article.get('volume', 'N/A'),
                        'issue': translated_article.get('issue', 'N/A'),
                        'year': time.strftime('%Y', time.localtime(translated_article['created_date']))
                    })
            payment['articles'] = articles
        elif payment['payment_type'] == 'subscription' and payment['ids']:
            # Get tariff details for subscription
            tariff_id = payment['ids'][0]
            tariff = dbc.tariffs.get(id=tariff_id).exec()
            if tariff:
                tariff_data = translate(tariff[0])
                payment['tariff'] = tariff_data
    
    return render_template(
        'dashboard/purchases.html',
        payments=payments,
        subscription_active=subscription_active,
        subscription_end_date=subscription_end_date,
        days_left=days_left
    )

@app.route("/dashboard/new_article")
@login_required
def app__dashboard_new_article():
    return render_template('dashboard/new_article.html')

@app.route("/dashboard/payments")
@login_required
def app__dashboard_payments():
    # Get user's payments
    payments = dbc.payments.get(user_id=session['user_id']).order_by('created_at').exec()
    
    # Get user data for subscription end date
    user = dbc.users.get(id=session['user_id']).exec()[0]
    
    subscription_active = False
    subscription_end_date = None
    days_left = None
    
    if user.get('subscription_end_date'):
        current_time = int(time.time())
        subscription_end_date = user['subscription_end_date']
        
        if subscription_end_date > current_time:
            subscription_active = True
            days_left = (subscription_end_date - current_time) // (24 * 60 * 60)
    
    # Get tariffs from database (exclude default tariffs)
    tariffs = dbc.tariffs.get().unequal(is_default=True).exec()
    
    # Apply translations to tariffs and determine currency based on user's country
    current_lang = session.get('language', 'en')
    user_country = None
    if user.get('country_id'):
        country_data = dbc.fix_country.get(id=user['country_id']).exec()
        if country_data:
            user_country = country_data[0]
    
    # Determine currency based on user's country or language
    currency = 'usd'  # default
    if user_country:
        if user_country.get('code') == 'UZ':
            currency = 'uzs'
        elif user_country.get('code') == 'RU':
            currency = 'rub'
    elif current_lang == 'uz':
        currency = 'uzs'
    elif current_lang == 'ru':
        currency = 'rub'
    
    # Process tariffs with translations and appropriate currency
    processed_tariffs = []
    for tariff in tariffs:
        tariff = translate(tariff)
        tariff['selected_price'] = tariff.get(f'price_{currency}', tariff.get('price_usd', 0))
        tariff['currency'] = currency.upper()
        processed_tariffs.append(tariff)
    
    # For article purchases, get article details
    for payment in payments:
        if payment['payment_type'] == 'article' and payment['ids']:
            # Get article details for each ID
            articles = []
            for article_id in payment['ids']:
                article = dbc.submissions.get(id=article_id).exec()
                if article:
                    # Apply translation to article data
                    translated_article = translate(article[0])
                    
                    # Get main author
                    main_author = None
                    if translated_article['main_author_id']:
                        author = dbc.author_profile.get(id=translated_article['main_author_id']).exec()
                        if author:
                            main_author = translate(author[0])
                    
                    articles.append({
                        'id': translated_article['id'],
                        'title': translated_article['title'],
                        'author': main_author['name'] if main_author else 'Unknown',
                        'volume': translated_article.get('volume', 'N/A'),
                        'issue': translated_article.get('issue', 'N/A'),
                        'year': time.strftime('%Y', time.localtime(translated_article['created_date']))
                    })
            payment['articles'] = articles
        elif payment['payment_type'] == 'subscription' and payment['ids']:
            # Get tariff details for subscription
            tariff_id = payment['ids'][0]
            tariff = dbc.tariffs.get(id=tariff_id).exec()
            if tariff:
                tariff_data = translate(tariff[0])
                payment['tariff'] = tariff_data
    
    return render_template(
        'dashboard/payments.html',
        payments=payments,
        subscription_active=subscription_active,
        subscription_end_date=subscription_end_date,
        days_left=days_left,
        tariffs=processed_tariffs,
        currency=currency
    )

@app.route("/dashboard/guides")
@login_required
def app__dashboard_guides():
    return render_template('dashboard/guides.html')

@app.route("/dashboard/profile", methods=['GET', 'POST'])
@login_required
def app__dashboard_profile():
    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'update_photo':
            if 'photo' not in request.files:
                return jsonify({'success': False, 'message': 'No file uploaded'})
            
            file = request.files['photo']
            if file.filename == '':
                return jsonify({'success': False, 'message': 'No file selected'})
            
            if file and allowed_file(file.filename):
                # Create secure filename with user_id to avoid conflicts
                filename = f"avatar_{session['user_id']}_{int(time.time())}.{file.filename.rsplit('.', 1)[1].lower()}"
                filepath = os.path.join(app.config['AVATARS_FOLDER'], filename)
                
                # Delete old avatar if exists
                user = dbc.users.get(id=session['user_id']).exec()[0]
                if user['avatar']:
                    try:
                        old_avatar_path = os.path.join('static', user['avatar'].lstrip('/'))
                        if os.path.exists(old_avatar_path):
                            os.remove(old_avatar_path)
                    except (FileNotFoundError, OSError):
                        pass
                
                # Save the file
                file.save(filepath)
                
                # Update user's avatar in database with relative path
                dbc.users.get(id=session['user_id']).update(
                    avatar=f"/static/uploads/avatars/{filename}"
                ).exec()
                
                return jsonify({'success': True})
            
            return jsonify({'success': False, 'message': 'Invalid file type'})
            
        elif action == 'delete_photo':
            user = dbc.users.get(id=session['user_id']).exec()[0]
            if user['avatar']:
                # Delete the file if it exists
                try:
                    os.remove(os.path.join('static', user['avatar'].lstrip('/')))
                except (FileNotFoundError, OSError):
                    pass
                
                # Update database
                dbc.users.get(id=session['user_id']).update(
                    avatar=None
                ).exec()
            
            return jsonify({'success': True})
            
        elif action == 'save_profile':
            # Validate country_id
            country_id = request.form.get('country_id')
            if country_id:
                country = dbc.fix_country.get(id=country_id).exec()
                if not country:
                    flash('Invalid country selected', 'error')
                    return redirect(url_for('app__dashboard_profile'))

            # Update user profile
            dbc.users.get(id=session['user_id']).update(
                name=request.form.get('name'),
                second_name=request.form.get('second_name'),
                father_name=request.form.get('father_name'),
                country_id=country_id
            ).exec()
            
            # Handle academic position in user_doc_uploads table
            academic_position = request.form.get('academic_position')
            document_path = request.form.get('document_path')
            
            # Get existing user_doc_upload record
            existing_doc_upload = dbc.user_doc_uploads.get(user_id=session['user_id']).exec()
            
            if academic_position and academic_position != '':
                # Save or update academic position record
                doc_upload_data = {
                    'user_id': session['user_id'],
                    'work_title': academic_position,
                    'file_path': document_path,
                    'verification_status': 'pending',
                    'updated_at': int(time.time())
                }
                
                if existing_doc_upload:
                    # Update existing record
                    dbc.user_doc_uploads.get(user_id=session['user_id']).update(**doc_upload_data).exec()
                else:
                    # Create new record
                    doc_upload_data['created_at'] = int(time.time())
                    dbc.user_doc_uploads.add(**doc_upload_data).exec()
            elif existing_doc_upload:
                # Remove academic position if set to "Не указан"
                dbc.user_doc_uploads.get(user_id=session['user_id']).delete().exec()
            
            flash('Profile updated successfully', 'success')
            return redirect(url_for('app__dashboard_profile'))
        
        elif action == 'save_author_profile':
            # Check if author profile exists
            author_profile = dbc.author_profile.get(user_id=session['user_id']).exec()
            
            # Get the ORCID from the form
            orcid = request.form.get('orcid')
            
            # Check if this ORCID is already connected to another user
            if orcid:
                existing_orcid = dbc.author_profile.get(orcid=orcid).unequal(user_id=None).exec()
                if existing_orcid and existing_orcid[0]['user_id'] != session['user_id']:
                    flash('This ORCID is already connected to another user', 'error')
                    return redirect(url_for('app__dashboard_profile'))
            
            profile_data = {
                'user_id': session['user_id'],
                'name': request.form.get('name'),
                'organization': request.form.get('organization'),
                'department': request.form.get('department'),
                'position': request.form.get('position'),
                'email': request.form.get('email'),
                'phone': request.form.get('phone'),
                'orcid': orcid,
                'address_street': request.form.get('address_street'),
                'address_city': request.form.get('address_city'),
                'address_country': request.form.get('address_country'),
                'address_zip': request.form.get('address_zip'),
                'updated_at': int(time.time())
            }
            
            if author_profile:
                # Update existing profile
                dbc.author_profile.get(user_id=session['user_id']).update(**profile_data).exec()
            else:
                # Create new profile
                profile_data['created_at'] = int(time.time())
                dbc.author_profile.add(**profile_data).exec()
                
            flash('Author profile updated successfully', 'success')
            return redirect(url_for('app__dashboard_profile'))
        
        elif action == 'upload_academic_document':
            if 'academic_document' not in request.files:
                return jsonify({'success': False, 'message': 'No file uploaded'})

            file = request.files['academic_document']
            if file.filename == '':
                return jsonify({'success': False, 'message': 'No file selected'})

            if file and allowed_file(file.filename, {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'}):
                # Create secure filename with user_id to avoid conflicts
                timestamp = int(time.time())
                filename = f"academic_doc_{session['user_id']}_{timestamp}.{file.filename.rsplit('.', 1)[1].lower()}"
                
                # Create documents folder if it doesn't exist
                DOCUMENTS_FOLDER = os.path.join(UPLOAD_FOLDER, 'documents')
                os.makedirs(DOCUMENTS_FOLDER, exist_ok=True)
                
                filepath = os.path.join(DOCUMENTS_FOLDER, filename)
                
                # Delete old document if exists
                existing_doc_upload = dbc.user_doc_uploads.get(user_id=session['user_id']).exec()
                if existing_doc_upload and existing_doc_upload[0].get('file_path'):
                    try:
                        old_doc_path = os.path.join('static', existing_doc_upload[0]['file_path'].lstrip('/'))
                        if os.path.exists(old_doc_path):
                            os.remove(old_doc_path)
                    except (FileNotFoundError, OSError):
                        pass
                
                # Save the file
                file.save(filepath)
                
                return jsonify({
                    'success': True,
                    'file_path': f"/static/uploads/documents/{filename}",
                    'filename': filename
                })

            return jsonify({'success': False, 'message': 'Invalid file type. Allowed: PDF, DOC, DOCX, JPG, PNG'})

    # Get user data for display
    user = dbc.users.get(id=session['user_id']).exec()[0]
    author_profile = dbc.author_profile.get(user_id=session['user_id']).exec()
    user_doc_upload = dbc.user_doc_uploads.get(user_id=session['user_id']).exec()
    fix_country = dbc.fix_country.get().exec()
    
    return render_template('dashboard/profile.html', 
                         user=user, 
                         author_profile=author_profile[0] if author_profile else None,
                         user_doc_upload=user_doc_upload[0] if user_doc_upload else None,
                         fix_country=fix_country)

@app.route("/api/getauthor", methods=['POST'])
@login_required
def app__api_getauthor():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format - JSON expected'})
    
    data_json = request.json
    orcid = data_json.get('orcid')
    
    if not orcid:
        return jsonify({'success': False, 'message': 'ORCID is required'})
    
    # Validate ORCID format (basic validation)
    if not orcid.strip() or len(orcid.strip()) < 10:
        return jsonify({'success': False, 'message': 'Invalid ORCID format. Please enter a valid ORCID (e.g., 0000-0000-0000-0000)'})
    
    try:
        # Search for author profile with this ORCID
        author_profile = dbc.author_profile.get(orcid=orcid.strip()).exec()
        
        if not author_profile:
            return jsonify({
                'success': True,
                'is_found': False,
                'message': f'No author found with ORCID: {orcid}'
            })
        
        # Get the author data
        author = author_profile[0]
        
        # Check if this ORCID is already connected to another user
        if author['user_id'] and author['user_id'] != session.get('user_id'):
            return jsonify({
                'success': False,
                'message': f'This ORCID ({orcid}) is already connected to another user account'
            })
        
        return jsonify({
            'success': True,
            'is_found': True,
            'author': {
                'id': author['id'],
                'name': author['name'],
                'orcid': author['orcid'],
                'organization': author['organization'],
                'department': author['department'],
                'position': author['position'],
                'email': author['email'],
                'phone': author['phone'],
                'address_street': author['address_street'],
                'address_city': author['address_city'],
                'address_country': author['address_country'],
                'address_zip': author['address_zip']
            }
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'Database error occurred: {str(e)}'})

@app.route("/api/getcurrentauthor", methods=['GET'])
@login_required
def app__api_getcurrentauthor():
    # Get author profile for current user
    author_profile = dbc.author_profile.get(user_id=session['user_id']).exec()
    
    if not author_profile:
        return jsonify({'success': True, 'author': None})
    
    author = author_profile[0]
    return jsonify({
        'success': True,
        'author': {
            'id': author['id'],
            'name': author['name'],
            'orcid': author['orcid'],
            'organization': author['organization'],
            'department': author['department'],
            'position': author['position'],
            'email': author['email'],
            'phone': author['phone'],
            'address_street': author['address_street'],
            'address_city': author['address_city'],
            'address_country': author['address_country'],
            'address_zip': author['address_zip']
        }
    })

@app.route("/api/getclassifications", methods=['GET'])
@login_required
def app__api_getclassifications():
    # Get all classifications
    classifications = dbc.fix_classifications.get().exec()
    
    if not classifications:
        return jsonify({
            'success': True,
            'classifications': {}
        })
    
    # Format classifications as a dictionary with id as key
    formatted_classifications = {}
    for classification in classifications:
        formatted_classifications[classification['id']] = classification['name']
    
    return jsonify({
        'success': True,
        'classifications': formatted_classifications
    })

# Article submission API endpoints
@app.route("/api/article/save", methods=['POST'])
@login_required
def app__api_article_save():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format'})
    
    data = request.get_json()
    
    # Validate required fields
    if not data.get('title'):
        return jsonify({'success': False, 'message': 'Title is required'})
    
    # Prepare submission data
    submission_data = {
        'title': data.get('title'),
        'abstract': data.get('abstract'),
        'is_special': data.get('is_special_issue'),
        'is_dataset': data.get('is_dataset'),
        'check_copyright': data.get('is_copyright_accept'),
        'keywords': data.get('keywords', []),
        'classifications': data.get('classifications', []),
        'check_ethical': data.get('is_ethical_accept'),
        'check_consent': data.get('is_consent_accept'),
        'check_acknowledgements': data.get('is_acknowledgements_accept'),
        'is_used_previous': data.get('is_previously'),
        'word_count': data.get('word_count'),
        'main_author_id': data.get('main_author_id'),
        'sub_author_ids': data.get('sub_author_ids', []),
        'is_competing_interests': data.get('is_competing'),
        'file_authors': data.get('file_authors'),
        'file_anonymized': data.get('file_anonymized'),
        'status': 'draft',
        'user_id': session['user_id'],
        'updated_at': int(time.time())
    }
    
    # Update existing submission or create new one
    submission_id = data.get('submission_id')
    if submission_id:
        # Update existing submission
        dbc.submissions.get(id=submission_id, user_id=session['user_id']).update(**submission_data).exec()
    else:
        # Create new submission
        submission_data['created_date'] = int(time.time())
        result = dbc.submissions.add(**submission_data).exec()
        if not result:
            return jsonify({'success': False, 'message': 'Failed to create submission'})
        submission_id = result[0]['id']
    
    # Return the updated submission data
    print(submission_data)
    return app__api_article_load(submission_id)

@app.route("/api/article/submit", methods=['POST'])
@login_required
def app__api_article_submit():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format'})
    
    data = request.json
    submission_id = data.get('submission_id')
    
    if not submission_id:
        return jsonify({'success': False, 'message': 'Submission ID is required'})
    
    # Verify ownership and draft status
    submission = dbc.submissions.get(id=submission_id, user_id=session['user_id']).exec()
    if not submission:
        return jsonify({'success': False, 'message': 'Submission not found'})
    
    submission = submission[0]
    if submission['status'] != 'draft':
        return jsonify({'success': False, 'message': 'Only draft submissions can be submitted'})
    
    # Validate required fields
    errors = []
    
    # Basic information validation
    if not submission.get('title'):
        errors.append('title')
    if not submission.get('abstract'):
        errors.append('abstract')
    if submission.get('is_special') is None:
        errors.append('is_special_issue')
    if submission.get('is_dataset') is None:
        errors.append('is_dataset')
    if not submission.get('keywords') or len(submission['keywords']) < 3:
        errors.append('keywords')
    if not submission.get('classifications') or len(submission['classifications']) == 0:
        errors.append('classifications')
    if not submission.get('word_count'):
        errors.append('word_count')
    if not submission.get('main_author_id'):
        errors.append('author')
    
    # File validation
    if not (submission.get('file_authors') and submission.get('file_anonymized')):
        errors.append('files')
    
    # Checkbox validation
    if not submission.get('check_copyright'):
        errors.append('is_copyright_accept')
    if not submission.get('check_ethical'):
        errors.append('is_ethical_accept')
    if not submission.get('check_consent'):
        errors.append('is_consent_accept')
    if not submission.get('check_acknowledgements'):
        errors.append('is_acknowledgements_accept')
    if submission.get('is_used_previous') is None:
        errors.append('is_previously')
    if submission.get('is_competing_interests') is None:
        errors.append('is_competing')
    
    if errors:
        result = {
            'success': False,
            'message': 'Please fill in all required fields and confirm all checkboxes',
            'errors': errors,
            'scroll_to': errors[0]
        }
        print('--------------------------------')
        print(json.dumps(result, indent=4))
        return jsonify(result)
    
    # Update submission status
    dbc.submissions.get(id=submission_id, user_id=session['user_id']).update(
        status='in_process',
        updated_at=int(time.time())
    ).exec()
    
    # Get updated submission and author profiles
    updated_submission = dbc.submissions.get(id=submission_id).exec()[0]
    updated_submission = translate(updated_submission)
    author_profiles = {}
    
    if updated_submission['main_author_id']:
        main_author = dbc.author_profile.get(id=updated_submission['main_author_id']).exec()
        if main_author:
            author_profiles[updated_submission['main_author_id']] = translate(main_author[0])
    
    if updated_submission['sub_author_ids']:
        for author_id in updated_submission['sub_author_ids']:
            if author_id not in author_profiles:
                co_author = dbc.author_profile.get(id=author_id).exec()
                if co_author:
                    author_profiles[author_id] = translate(co_author[0])
    
    result = {
        'success': True,
        'message': 'Article submitted successfully',
        'submission': updated_submission,
        'author_profiles': author_profiles
    }
    print(json.dumps(result, indent=4))
    return jsonify(result)

@app.route('/api/article/upload', methods=['POST'])
@login_required
def api__article_upload():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file uploaded'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'})

    file_type = request.form.get('type')
    if file_type not in ['authors', 'anonymized']:
        return jsonify({'success': False, 'message': 'Invalid file type'})

    submission_id = request.form.get('submission_id')
    
    if file and allowed_file(file.filename, {'pdf', 'doc', 'docx'}):
        # Create secure filename
        timestamp = int(time.time())
        filename = f"{file_type}_{submission_id or 'new'}_{timestamp}.{file.filename.rsplit('.', 1)[1].lower()}"
        filepath = os.path.join(app.config['ARTICLES_FOLDER'], filename)
        
        # Save the file
        file.save(filepath)
        
        # Return success with the relative URL for download
        return jsonify({
            'success': True,
            'filename': filename,
            'download': f"/static/uploads/articles/{filename}"
        })

    return jsonify({'success': False, 'message': 'Invalid file type'})

@app.route("/api/article/load/<int:submission_id>")
@login_required
def app__api_article_load(submission_id):
    # Get submission with author information
    submission = dbc.submissions.get(id=submission_id, user_id=session['user_id']).exec()
    if not submission:
        return jsonify({'success': False, 'message': 'Submission not found'})
    
    submission = translate(submission[0])
    
    # Get author profiles
    author_profiles = {}
    if submission['main_author_id']:
        main_author = dbc.author_profile.get(id=submission['main_author_id']).exec()
        if main_author:
            author_profiles[submission['main_author_id']] = translate(main_author[0])
    
    if submission['sub_author_ids']:
        for author_id in submission['sub_author_ids']:
            if author_id not in author_profiles:
                co_author = dbc.author_profile.get(id=author_id).exec()
                if co_author:
                    author_profiles[author_id] = translate(co_author[0])
    
    # Format submission data for frontend
    formatted_submission = {
        'id': submission['id'],
        'title': submission['title'],
        'abstract': submission['abstract'],
        'is_special_issue': submission['is_special'],
        'is_dataset': submission['is_dataset'],
        'check_copyright': submission['check_copyright'],
        'keywords': submission['keywords'] or [],
        'classifications': submission['classifications'] or [],
        'check_ethical': submission['check_ethical'],
        'check_consent': submission['check_consent'],
        'check_acknowledgements': submission['check_acknowledgements'],
        'is_used_previous': submission['is_used_previous'],
        'word_count': submission['word_count'],
        'is_corresponding_author': submission['is_corresponding_author'],
        'main_author_id': submission['main_author_id'],
        'sub_author_ids': submission['sub_author_ids'] or [],
        'is_competing_interests': submission['is_competing_interests'],
        'notes': submission['notes'],
        'status': submission['status']
    }

    # Add file information if files exist
    if submission['file_authors']:
        formatted_submission['file_authors'] = {
            'title': submission['file_authors'],
            'download': f"/static/uploads/articles/{submission['file_authors']}"
        }
    
    if submission['file_anonymized']:
        formatted_submission['file_anonymized'] = {
            'title': submission['file_anonymized'],
            'download': f"/static/uploads/articles/{submission['file_anonymized']}"
        }
    
    # Check if this is a view-only submission
    is_view_only = submission['status'] != 'draft'
    
    # Get all classifications for the frontend
    classifications = dbc.fix_classifications.get().exec()
    formatted_classifications = {}
    for classification in classifications:
        formatted_classifications[str(classification['id'])] = classification['name']
    print(formatted_submission)
    return jsonify({
        'success': True,
        'submission': formatted_submission,
        'author_profiles': author_profiles,
        'classifications': formatted_classifications,
        'is_view_only': is_view_only,
        'can_edit': not is_view_only,
        'is_ready_submit': True  # This will be used by the frontend to enable/disable the submit button
    })

@app.route("/api/payment/submit_proof", methods=['POST'])
@login_required
def api__payment_submit_proof():
    if 'proof' not in request.files:
        return jsonify({'success': False, 'message': 'No file uploaded'})
    
    payment_id = request.form.get('payment_id')
    if not payment_id:
        return jsonify({'success': False, 'message': 'Payment ID is required'})
    
    # Get the payment and verify ownership
    payment = dbc.payments.get(id=payment_id, user_id=session['user_id']).exec()
    if not payment:
        return jsonify({'success': False, 'message': 'Payment not found'})
    
    payment = payment[0]
    if payment['status'] != 'unpaid':
        return jsonify({'success': False, 'message': 'This payment is not in unpaid status'})
    
    file = request.files['proof']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'})
    
    if file and allowed_file(file.filename, {'pdf', 'jpg', 'jpeg', 'png'}):
        # Create secure filename with payment_id to avoid conflicts
        filename = f"payment_proof_{payment_id}_{int(time.time())}.{file.filename.rsplit('.', 1)[1].lower()}"
        
        # Create payments folder if it doesn't exist
        PAYMENTS_FOLDER = os.path.join(UPLOAD_FOLDER, 'payments')
        os.makedirs(PAYMENTS_FOLDER, exist_ok=True)
        
        filepath = os.path.join(PAYMENTS_FOLDER, filename)
        
        # Save the file
        file.save(filepath)
        
        # Update payment record
        dbc.payments.get(id=payment_id).update(
            proof=f"/static/uploads/payments/{filename}",
            note=request.form.get('note'),
            status='pending'
        ).exec()
        
        return jsonify({'success': True})
    
    return jsonify({'success': False, 'message': 'Invalid file type'})

@app.route("/api/payment/delete/<int:payment_id>", methods=['POST'])
@login_required
def api__payment_delete(payment_id):
    # Get the payment and verify ownership
    payment = dbc.payments.get(id=payment_id, user_id=session['user_id']).exec()
    if not payment:
        return jsonify({'success': False, 'message': 'Payment not found'})
    
    payment = payment[0]
    if payment['status'] != 'unpaid':
        return jsonify({'success': False, 'message': 'Only unpaid payments can be deleted'})
    
    # If there's a proof file, delete it
    if payment.get('proof'):
        try:
            file_path = os.path.join('static', payment['proof'].lstrip('/'))
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"Error deleting file: {e}")
    
    # Delete the payment record
    dbc.payments.get(id=payment_id, user_id=session['user_id']).delete().exec()
    
    return jsonify({'success': True})

@app.route("/api/payment/create_subscription", methods=['POST'])
@login_required
def api__payment_create_subscription():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format'})
    
    data = request.get_json()
    tariff_id = data.get('tariff_id')
    
    if not tariff_id:
        return jsonify({'success': False, 'message': 'Tariff ID is required'})
    
    # Get tariff from database
    tariff = dbc.tariffs.get(id=tariff_id).exec()
    if not tariff:
        return jsonify({'success': False, 'message': 'Invalid tariff'})
    
    tariff = tariff[0]
    
    # Get user data to determine currency
    user = dbc.users.get(id=session['user_id']).exec()[0]
    current_lang = session.get('language', 'en')
    
    # Determine currency and price
    currency = 'usd'  # default
    if user.get('country_id'):
        country_data = dbc.fix_country.get(id=user['country_id']).exec()
        if country_data:
            user_country = country_data[0]
            if user_country.get('code') == 'UZ':
                currency = 'uzs'
            elif user_country.get('code') == 'RU':
                currency = 'rub'
    elif current_lang == 'uz':
        currency = 'uzs'
    elif current_lang == 'ru':
        currency = 'rub'
    
    # Get price in appropriate currency
    price = tariff.get(f'price_{currency}', tariff.get('price_usd', 0))
    
    # Create payment record
    payment_data = {
        'user_id': session['user_id'],
        'status': 'unpaid',
        'payment_type': 'subscription',
        'amount': float(price),
        'ids': [tariff_id],
        'currency': currency,
        'created_at': int(time.time())
    }
    
    result = dbc.payments.add(**payment_data).exec()
    if not result:
        return jsonify({'success': False, 'message': 'Failed to create payment'})
    
    return jsonify({'success': True})

@app.route("/api/issue/purchase", methods=['POST'])
@login_required
def api__issue_purchase():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format'})
    
    data = request.get_json()
    issue_id = data.get('issue_id')
    
    if not issue_id:
        return jsonify({'success': False, 'message': 'Issue ID is required'})
    
    # Get issue from database
    issue = dbc.issues.get(id=issue_id).exec()
    if not issue:
        return jsonify({'success': False, 'message': 'Issue not found'})
    
    issue = issue[0]
    
    # Check if issue is paid
    if not issue.get('is_paid'):
        return jsonify({'success': False, 'message': 'This issue is free'})
    
    # Check if user already has this issue
    existing_payments = dbc.payments.get(user_id=session['user_id'], payment_type='issue', status='paid').exec()
    for payment in existing_payments:
        if payment.get('ids') and issue_id in payment['ids']:
            return jsonify({'success': False, 'message': 'You already own this issue'})
    
    # Create payment record
    payment_data = {
        'user_id': session['user_id'],
        'status': 'unpaid',
        'payment_type': 'issue',
        'amount': float(issue.get('price', 0)),
        'ids': [issue_id],
        'created_at': int(time.time())
    }
    
    result = dbc.payments.add(**payment_data).exec()
    if not result:
        return jsonify({'success': False, 'message': 'Failed to create payment'})
    
    return jsonify({'success': True, 'message': 'Payment created successfully'})

@app.route("/api/translations/clear_cache", methods=['POST'])
def api__clear_translations_cache():
    """API endpoint для очистки кеша переводов"""
    try:
        clear_translations_cache()
        return jsonify({'success': True, 'message': 'Translations cache cleared successfully'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error clearing cache: {str(e)}'})

@app.route("/api/profile/change_password", methods=['POST'])
@login_required
def api__profile_change_password():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format'})

    data = request.get_json()
    new_password = data.get('new_password')
    confirm_password = data.get('confirm_password')
    
    if not new_password or not confirm_password:
        return jsonify({'success': False, 'message': 'Both password fields are required'})
    
    if new_password != confirm_password:
        return jsonify({'success': False, 'message': 'Passwords do not match'})
    
    # Update user's password
    dbc.users.get(id=session['user_id']).update(
        password=new_password
    ).exec()
    
    return jsonify({'success': True, 'message': 'Password updated successfully'})

@app.route("/article/<int:article_id>")
def app__article(article_id):
    # Get the publication instead of article
    publication = dbc.publications.get(id=article_id).exec()
    if not publication:
        flash('Article not found', 'error')
        return redirect(url_for('app__index'))
    
    publication = publication[0]
    
    # Apply translation to publication
    publication = translate(publication)
    
    # Increment view count
    current_views = publication.get('stat_views', 0)
    dbc.publications.get(id=article_id).update(
        stat_views=current_views + 1
    ).exec()
    publication['stat_views'] = current_views + 1
    
    # Get main author
    main_author = None
    if publication['main_author_id']:
        author = dbc.author_profile.get(id=publication['main_author_id']).exec()
        if author:
            main_author = author[0]
            main_author = translate(main_author)
    
    # Get co-authors
    co_authors = []
    if publication['subauthor_ids']:
        for author_id in publication['subauthor_ids']:
            author = dbc.author_profile.get(id=author_id).exec()
            if author:
                author_data = translate(author[0])
                co_authors.append(author_data)
    
    # Get issue information
    issue = None
    if publication['issue_id']:
        issue_data = dbc.issues.get(id=publication['issue_id']).exec()
        if issue_data:
            issue = translate(issue_data[0])
    
    # Get publication parts (content sections) and apply translation
    publication_parts = dbc.publication_parts.get(publication_id=article_id).order_by('order_id').exec()
    for part in publication_parts:
        part = translate(part)
    
    # Get references (what this publication cites) and apply translation
    publication_refs = dbc.publication_refs.get(publication_id=article_id).exec()
    for ref in publication_refs:
        ref = translate(ref)
    
    # Get citations (who cites this publication) and apply translation
    publication_citations = dbc.publication_citations.get(publication_id=article_id).exec()
    for citation in publication_citations:
        citation = translate(citation)
    
    # Count citations and references
    publication['citations_count'] = len(publication_citations)
    publication['references_count'] = len(publication_refs)
    
    # Check if user has access to this publication
    has_access = False
    if 'user_id' in session:
        # Check if user has active subscription
        user = dbc.users.get(id=session['user_id']).exec()[0]
        if user.get('subscription_end_date') and user['subscription_end_date'] > int(time.time()):
            has_access = True
        else:
            # Check if user has purchased this issue
            payments = dbc.payments.get(user_id=session['user_id'], status='paid').exec()
            for payment in payments:
                if payment['payment_type'] == 'issue' and payment['ids'] and publication['issue_id'] in payment['ids']:
                    has_access = True
                    break
    
    return render_template('mainweb/article.html',
                         publication=publication,
                         main_author=main_author,
                         co_authors=co_authors,
                         issue=issue,
                         has_access=has_access,
                         publication_parts=publication_parts,
                         publication_refs=publication_refs,
                         publication_citations=publication_citations)

@app.route("/article/download/<int:article_id>")
@login_required  # Require login to download
def app__download_article(article_id):
    if 'user_id' not in session:
        return redirect(url_for('app__login'))
    
    # Get the publication instead of article
    publication = dbc.publications.get(id=article_id).exec()
    if not publication:
        flash('Article not found', 'error')
        return redirect(url_for('app__index'))
    
    publication = publication[0]
    
    # Check if user has access to this publication
    has_access = False
    user = dbc.users.get(id=session['user_id']).exec()[0]
    
    # Check subscription
    if user.get('subscription_end_date') and user['subscription_end_date'] > int(time.time()):
        has_access = True
    else:
        # Check if issue is purchased
        payments = dbc.payments.get(user_id=session['user_id'], status='paid').exec()
        for payment in payments:
            if payment['payment_type'] == 'issue' and payment['ids'] and publication['issue_id'] in payment['ids']:
                has_access = True
                break
    
    if not has_access:
        flash('You do not have access to download this article', 'error')
        return redirect(url_for('app__article', article_id=article_id))
    
    # Get the file path from file_ids
    if not publication['file_ids'] or len(publication['file_ids']) == 0:
        flash('Article file not found', 'error')
        return redirect(url_for('app__article', article_id=article_id))
    
    # Get the main PDF file (assuming it's the first file)
    file_data = dbc.files.get(id=publication['file_ids'][0]).exec()
    if not file_data:
        flash('Article file not found', 'error')
        return redirect(url_for('app__article', article_id=article_id))
    
    file_path = os.path.join(app.config['ARTICLES_FOLDER'], file_data[0]['filename'])
    if not os.path.exists(file_path):
        flash('Article file not found', 'error')
        return redirect(url_for('app__article', article_id=article_id))
    
    # Return the file
    return send_file(file_path,
                    as_attachment=True,
                    download_name=f"{publication['title']}.pdf")

@app.context_processor
def inject_latest_issue():
    latest_issue = dbc.issues.get().order_by('year').order_by('issue_no').per_page(1).page(1).exec()
    return {
        'latest_issue': latest_issue[0] if latest_issue else None
    }

@app.context_processor
def inject_translations():
    return dict(t=t)

@app.route("/articles")
def app__articles():
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # Get filter parameters
    search_query = request.args.get('search', '').strip()
    issue_filter = request.args.get('issue', '')
    volume_filter = request.args.get('volume', '')
    year_filter = request.args.get('year', '')
    access_filter = request.args.get('access', '')
    sort_by = request.args.get('sort', 'newest')
    
    # Start building the query
    query = dbc.publications.get()
    
    # Apply filters
    if issue_filter:
        query = query.where(issue_id=int(issue_filter))
    
    if year_filter:
        # Get issues for the specified year
        year_issues = dbc.issues.get(year=int(year_filter)).exec()
        if year_issues:
            issue_ids = [issue['id'] for issue in year_issues]
            query = query.any(issue_id=issue_ids)
        else:
            # No issues for this year, return empty result
            query = query.get(id=-1)
    
    if volume_filter:
        # Get issues for the specified volume
        volume_issues = dbc.issues.get(vol_no=volume_filter).exec()
        if volume_issues:
            issue_ids = [issue['id'] for issue in volume_issues]
            query = query.any(issue_id=issue_ids)
        else:
            # No issues for this volume, return empty result
            query = query.get(id=-1)
    
    if access_filter:
        if access_filter == 'open':
            query = query.where(is_paid=False)
        elif access_filter == 'paid':
            query = query.where(is_paid=True, subscription_enable=False)
        elif access_filter == 'subscription':
            query = query.where(subscription_enable=True)
    
    # Apply sorting (note: connector always sorts DESC)
    if sort_by == 'newest':
        query = query.order_by('date_publish')  # DESC by default - newest first
    elif sort_by == 'oldest':
        # For oldest first, we'll need to reverse after getting results
        query = query.order_by('date_publish')
    elif sort_by == 'title_az':
        # For A-Z, we'll need to sort after getting results
        query = query.order_by('title')
    elif sort_by == 'title_za':
        query = query.order_by('title')  # DESC by default - Z-A
    elif sort_by == 'most_viewed':
        query = query.order_by('stat_views')  # DESC by default - most viewed first
    elif sort_by == 'most_cited':
        # We'll sort by citations_count after processing, since it's calculated
        pass  # Will be sorted after processing
    else:
        query = query.order_by('date_publish')  # Default: newest first
    
    # Execute query with pagination
    publications = query.per_page(per_page).page(page).exec()
    
    # Post-process sorting for cases that need reversal or special handling
    if sort_by == 'oldest':
        publications = list(reversed(publications))
    elif sort_by == 'title_az':
        publications = sorted(publications, key=lambda x: x.get('title', '').lower())
    
    # Apply search filter if provided
    if search_query:
        filtered_publications = []
        for pub in publications:
            # Search in title, abstract, and keywords
            search_fields = [
                pub.get('title', '').lower(),
                pub.get('abstract', '').lower(),
                ' '.join(pub.get('keywords', [])).lower()
            ]
            
            # Get author names for search
            author_names = []
            if pub['main_author_id']:
                main_author = dbc.author_profile.get(id=pub['main_author_id']).exec()
                if main_author:
                    author_names.append(main_author[0]['name'].lower())
            
            if pub['subauthor_ids']:
                for author_id in pub['subauthor_ids']:
                    co_author = dbc.author_profile.get(id=author_id).exec()
                    if co_author:
                        author_names.append(co_author[0]['name'].lower())
            
            search_fields.extend(author_names)
            
            # Check if search query matches any field
            if any(search_query.lower() in field for field in search_fields):
                filtered_publications.append(pub)
        
        publications = filtered_publications
    
    # Process publications to get author information and issue details
    processed_publications = []
    for pub in publications:
        # Get main author
        main_author = None
        if pub['main_author_id']:
            main_authors = dbc.author_profile.get(id=pub['main_author_id']).exec()
            if main_authors:
                main_author = main_authors[0]
        
        # Get co-authors
        co_authors = []
        if pub['subauthor_ids']:
            for author_id in pub['subauthor_ids']:
                co_authors_result = dbc.author_profile.get(id=author_id).exec()
                if co_authors_result:
                    co_authors.append(co_authors_result[0])
        
        # Format authors string
        authors = main_author['name'] if main_author else ''
        if co_authors:
            if authors:
                authors += ', '
            authors += ', '.join(author['name'] for author in co_authors)
        
        # Get issue information
        issue = None
        if pub['issue_id']:
            issue_data = dbc.issues.get(id=pub['issue_id']).exec()
            if issue_data:
                issue = issue_data[0]
        
        # Count references (what this publication cites)
        references_count = len(dbc.publication_refs.get(publication_id=pub['id']).exec())
        
        # Count citations (who cites this publication)  
        citations_count = len(dbc.publication_citations.get(publication_id=pub['id']).exec())
        
        # Add processed publication
        processed_publications.append({
            'id': pub['id'],
            'title': pub['title'],
            'abstract': pub['abstract'],
            'authors': authors,
            'date_publish': pub['date_publish'],
            'stat_views': pub.get('stat_views', 0),
            'stat_crossref': pub.get('stat_crossref', 0),
            'references_count': references_count,
            'citations_count': citations_count,
            'doi': pub.get('doi'),
            'keywords': pub.get('keywords', []),
            'is_paid': pub.get('is_paid', False),
            'subscription_enable': pub.get('subscription_enable', False),
            'issue': issue
        })
    
    # Post-process sorting by citations if needed
    if sort_by == 'most_cited':
        processed_publications = sorted(processed_publications, key=lambda x: x['citations_count'], reverse=True)
    
    # Get filter options
    all_issues = dbc.issues.get().order_by('year').exec()
    all_volumes = sorted(list(set([issue['vol_no'] for issue in all_issues if issue['vol_no']])), reverse=True)
    all_years = sorted(list(set([issue['year'] for issue in all_issues if issue['year']])), reverse=True)
    
    return render_template('mainweb/articles.html',
                         publications=processed_publications,
                         all_issues=all_issues,
                         all_volumes=all_volumes,
                         all_years=all_years,
                         current_filters={
                             'search': search_query,
                             'issue': issue_filter,
                             'volume': volume_filter,
                             'year': year_filter,
                             'access': access_filter,
                             'sort': sort_by
                         },
                         total_results=len(processed_publications),
                         page=page,
                         per_page=per_page)

@app.route("/news")
def app__news():
    page = request.args.get('page', 1, type=int)
    per_page = 12
    
    # Get all news and announcements
    all_items = dbc.news.get(status='published').order_by('published_at').per_page(per_page).page(page).exec()
    news_items = dbc.news.get(type='news', status='published').order_by('published_at').exec()
    announcements = dbc.news.get(type='announcement', status='published').order_by('published_at').exec()
    
    # Apply translations
    for item in all_items + news_items + announcements:
        item = translate(item)
    
    return render_template('mainweb/news.html',
                         all_items=all_items,
                         news_items=news_items,
                         announcements=announcements)

@app.route("/news/<int:news_id>")
def app__news_detail(news_id):
    # Get the news item
    news_item = dbc.news.get(id=news_id, status='published').exec()
    if not news_item:
        flash('News item not found', 'error')
        return redirect(url_for('app__news'))
    
    news_item = news_item[0]
    news_item = translate(news_item)
    
    # Get author information if available
    author = None
    if news_item.get('author_id'):
        author_data = dbc.author_profile.get(id=news_item['author_id']).exec()
        if author_data:
            author = author_data[0]
    
    # Get related items (same type, excluding current)
    related_items = dbc.news.get(type=news_item['type'], status='published').unequal(id=news_id).order_by('published_at').per_page(3).page(1).exec()
    for item in related_items:
        item = translate(item)
    
    return render_template('mainweb/news_detail.html',
                         news_item=news_item,
                         author=author,
                         related_items=related_items)

@app.route("/change_language/<string:lang>")
def app__change_language(lang):
    # Validate language code
    if lang in ['en', 'uz', 'ru']:
        session['language'] = lang
    return redirect(request.referrer or url_for('app__index'))

@app.route("/api/createauthor", methods=['POST'])
@login_required
def app__api_createauthor():
    if not request.is_json:
        return jsonify({'success': False, 'message': 'Invalid request format - JSON expected'})
    
    data = request.get_json()
    orcid = data.get('orcid')
    name = data.get('name')
    
    if not orcid or not name:
        missing_fields = []
        if not orcid:
            missing_fields.append('ORCID')
        if not name:
            missing_fields.append('name')
        return jsonify({'success': False, 'message': f'Required fields missing: {", ".join(missing_fields)}'})
    
    # Validate ORCID format (basic validation)
    if not orcid.strip() or len(orcid.strip()) < 10:
        return jsonify({'success': False, 'message': 'Invalid ORCID format. Please enter a valid ORCID (e.g., 0000-0000-0000-0000)'})
    
    # Validate name
    if not name.strip() or len(name.strip()) < 2:
        return jsonify({'success': False, 'message': 'Author name must be at least 2 characters long'})
    
    try:
        # Check if this ORCID already exists
        existing_author = dbc.author_profile.get(orcid=orcid.strip()).exec()
        if existing_author:
            return jsonify({'success': False, 'message': f'Author with ORCID {orcid} already exists in the database'})
        
        # Create new author profile (not connected to any user)
        profile_data = {
            'user_id': None,  # Co-author, not connected to a user account
            'name': name.strip(),
            'organization': data.get('organization', '').strip(),
            'department': data.get('department', '').strip(),
            'position': data.get('position', '').strip(),
            'email': data.get('email', '').strip(),
            'phone': data.get('phone', '').strip(),
            'orcid': orcid.strip(),
            'address_street': data.get('address_street', '').strip(),
            'address_city': data.get('address_city', '').strip(),
            'address_country': data.get('address_country', '').strip(),
            'address_zip': data.get('address_zip', '').strip(),
            'created_at': int(time.time()),
            'updated_at': int(time.time())
        }
        
        result = dbc.author_profile.add(**profile_data).exec()
        if result:
            new_author = result[0]
            return jsonify({
                'success': True,
                'message': f'Author "{name}" created successfully',
                'author': {
                    'id': new_author['id'],
                    'name': new_author['name'],
                    'orcid': new_author['orcid'],
                    'organization': new_author['organization'],
                    'department': new_author['department'],
                    'position': new_author['position'],
                    'email': new_author['email'],
                    'phone': new_author['phone'],
                    'address_street': new_author['address_street'],
                    'address_city': new_author['address_city'],
                    'address_country': new_author['address_country'],
                    'address_zip': new_author['address_zip']
                }
            })
        else:
            return jsonify({'success': False, 'message': 'Failed to create author: Database operation returned no results'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'Database error occurred while creating author: {str(e)}'})

@app.route("/issues")
def app__issues():
    # Get all issues ordered by year and issue number
    issues = dbc.issues.get().order_by('year').order_by('issue_no').exec()
    
    return render_template('mainweb/issues.html', issues=issues)

@app.route("/issue/<int:issue_id>")
def app__issue(issue_id):
    # Get the issue
    issue = dbc.issues.get(id=issue_id).exec()
    if not issue:
        flash('Issue not found', 'error')
        return redirect(url_for('app__issues'))
    
    issue = issue[0]
    
    # Get all issues ordered by year and issue number
    all_issues = dbc.issues.get().exec()
    all_issues = sorted(all_issues, key=lambda x: (x['year'], x['issue_no']))
    
    # Find current issue's index
    current_index = None
    for i, curr_issue in enumerate(all_issues):
        if curr_issue['id'] == issue_id:
            current_index = i
            break
    
    # Get previous and next issues
    prev_issue = all_issues[current_index - 1] if current_index > 0 else None
    next_issue = all_issues[current_index + 1] if current_index < len(all_issues) - 1 else None
    
    # Check if user has access to this issue
    has_access = False
    if 'user_id' in session:
        # Check if user has active subscription
        user = dbc.users.get(id=session['user_id']).exec()[0]
        if user.get('subscription_end_date') and user['subscription_end_date'] > int(time.time()):
            has_access = True
        else:
            # Check if user has purchased this issue
            payments = dbc.payments.get(user_id=session['user_id'], status='paid').exec()
            for payment in payments:
                if payment['payment_type'] == 'issue' and payment['ids'] and issue_id in payment['ids']:
                    has_access = True
                    break
    
    # Get publications for this issue
    publications = dbc.publications.get(issue_id=issue_id).exec()
    articles = []
    
    # Process publications to get author information
    if publications:
        for pub in publications:
            # Get main author
            main_author = None
            if pub['main_author_id']:
                main_authors = dbc.author_profile.get(id=pub['main_author_id']).exec()
                if main_authors:
                    main_author = main_authors[0]
            
            # Get co-authors
            co_authors = []
            if pub['subauthor_ids']:
                for author_id in pub['subauthor_ids']:
                    co_authors_result = dbc.author_profile.get(id=author_id).exec()
                    if co_authors_result:
                        co_authors.append(co_authors_result[0])
            
            # Format authors string
            authors = main_author['name'] if main_author else ''
            if co_authors:
                if authors:
                    authors += ', '
                authors += ', '.join(author['name'] for author in co_authors)
            
            # Add to articles list with the structure expected by the template
            articles.append({
                'id': pub['id'],
                'title': pub['title'],
                'authors': authors
            })
    
    return render_template('mainweb/issue.html', 
                         issue=issue, 
                         has_access=has_access,
                         prev_issue=prev_issue,
                         next_issue=next_issue,
                         articles=articles)

@app.route("/issue/purchase/<int:issue_id>")
@login_required
def app__purchase_issue(issue_id):
    # Redirect to issues page - this route is now handled by AJAX
    return redirect(url_for('app__issues'))

app.run(host = '0.0.0.0', port = 16534, debug= True)