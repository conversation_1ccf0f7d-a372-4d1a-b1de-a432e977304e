<div class="flex items-center justify-between">
    <div class="pb-0">
        <a href="/" class="flex items-center content-center">
            <img src="{{ url_for('static', filename='logo.png') }}" style="height: {{ logo_height|default('40') }}px;" alt="{{ t('website_title') }}">
            {% if show_title|default(true) %}
            <div class="text-xl font-medium ms-4 font-serif">{{ t('website_title') }}</div>
            {% endif %}
        </a>
    </div>
    <div class="flex space-x-6">
        <div class="pb-1">
        </div>
        <div class="relative inline-block text-left pb-1">
            <div>
                <button type="button" class="inline-flex w-fit px-2 py-1 mt-1 items-center justify-center hover:cursor-pointer pb-2 hover:opacity-75 transition-opacity" id="menu-button" aria-expanded="true" aria-haspopup="true">
                    <span class="fi fi-{% if session.get('language') == 'uz' %}uz{% elif session.get('language') == 'ru' %}ru{% else %}gb{% endif %} max-h-3"></span>
                </button>
            </div>

            <div class="absolute right-0 z-10 mt-2 hidden w-56 origin-top-right rounded-md bg-white ring-1 shadow-lg ring-black/5 focus:outline-hidden" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                <div class="py-1" role="none">
                    <a href="{{ url_for('app__change_language', lang='uz') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors" role="menuitem" tabindex="-1"><span class="fi fi-uz max-h-3"></span> {{ t('language_uz') }}</a>
                    <a href="{{ url_for('app__change_language', lang='ru') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors" role="menuitem" tabindex="-1"><span class="fi fi-ru max-h-3"></span> {{ t('language_ru') }}</a>
                    <a href="{{ url_for('app__change_language', lang='en') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors" role="menuitem" tabindex="-1"><span class="fi fi-gb max-h-3"></span> {{ t('language_en') }}</a>
                </div>
            </div>
        </div>

        {% if session.get('user_id') %}
            <a href="{{ url_for('app__dashboard') }}" class="border-b-1 border-green-400/0 hover:border-b-1 pb-1 hover:border-green-400 transition-colors group">
                <iconify-icon icon="solar:user-bold-duotone" class="-mb-0.5 group-hover:text-green-600 transition-colors"></iconify-icon> {{ t('my_account') }}
            </a>
            {% if session.get('user', {}).get('rolename') in ['admin', 'editor'] %}
            <a href="http://localhost:5000/fmadmin" target="_blank" class="border-b-1 border-blue-400/0 hover:border-b-1 pb-1 hover:border-blue-400 transition-colors group">
                <iconify-icon icon="solar:settings-bold-duotone" class="-mb-0.5 group-hover:text-blue-600 transition-colors"></iconify-icon> {{ t('admin_panel') }}
            </a>
            {% endif %}
            <a href="{{ url_for('app__logout') }}" class="border-b-1 border-green-400/0 hover:border-b-1 pb-1 hover:border-green-400 transition-colors group">
                <iconify-icon icon="solar:logout-3-bold-duotone" class="-mb-0.5 text-gray-500 group-hover:text-red-600 transition-colors"></iconify-icon> {{ t('logout') }}
            </a>
        {% else %}
            <a href="{{ url_for('app__login') }}" class="border-b-1 border-green-400/0 hover:border-b-1 pb-1 hover:border-green-400 transition-colors group">
                <iconify-icon icon="solar:login-3-bold-duotone" class="-mb-0.5 text-gray-500 group-hover:text-green-600 transition-colors"></iconify-icon> {{ t('login') }}
            </a>
        {% endif %}
    </div>
</div> 