#!/usr/bin/env python3
"""
Скрипт для миграции статических переводов в базу данных
Переносит переводы из mainweb/modules/translations.py в таблицу translations
"""

import sys
import os
import time

# Добавляем пути к модулям
sys.path.append(os.path.join(os.path.dirname(__file__), 'mainweb'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'fmadmin'))

from mainweb.modules.translations import UZ, RU, EN
from fmadmin.connector import PostgreSQLConnector

def migrate_translations():
    """Мигрирует статические переводы в базу данных"""
    
    # Подключение к БД (используем настройки из fmadmin)
    try:
        db = PostgreSQLConnector(
            database='journal', 
            user='postgres', 
            password='1', 
            host='127.0.0.1', 
            port='5432'
        )
        print("✅ Подключение к базе данных установлено")
    except Exception as e:
        print(f"❌ Ошибка подключения к БД: {e}")
        return False

    # Получаем все ключи переводов
    all_keys = set()
    all_keys.update(EN.keys())
    all_keys.update(RU.keys()) 
    all_keys.update(UZ.keys())
    
    print(f"📊 Найдено {len(all_keys)} ключей переводов")
    
    # Проверяем существующие переводы в БД
    existing_translations = {}
    try:
        existing = db.translations.all().exec()
        for trans in existing:
            existing_translations[trans['alias']] = trans
        print(f"📋 В БД уже есть {len(existing_translations)} переводов")
    except Exception as e:
        print(f"⚠️ Ошибка при получении существующих переводов: {e}")
        existing_translations = {}

    # Счетчики
    added_count = 0
    updated_count = 0
    skipped_count = 0
    
    # Мигрируем каждый ключ
    for key in sorted(all_keys):
        # Получаем переводы для всех языков
        content_en = EN.get(key, '')
        content_ru = RU.get(key, '')
        content_uz = UZ.get(key, '')
        
        # Пропускаем служебные ключи
        if key in ['alias']:
            skipped_count += 1
            continue
            
        try:
            if key in existing_translations:
                # Обновляем существующий перевод
                existing = existing_translations[key]
                
                # Проверяем, нужно ли обновление
                needs_update = (
                    existing.get('content') != content_en or
                    existing.get('content_ru') != content_ru or
                    existing.get('content_uz') != content_uz
                )
                
                if needs_update:
                    db.translations.all().equal(alias=key).update(
                        content=content_en,
                        content_ru=content_ru,
                        content_uz=content_uz
                    ).exec()
                    updated_count += 1
                    print(f"🔄 Обновлен: {key}")
                else:
                    skipped_count += 1
            else:
                # Добавляем новый перевод
                db.translations.add(
                    alias=key,
                    content=content_en,
                    content_ru=content_ru,
                    content_uz=content_uz,
                    created_at=int(time.time())
                ).exec()
                added_count += 1
                print(f"➕ Добавлен: {key}")
                
        except Exception as e:
            print(f"❌ Ошибка при обработке ключа '{key}': {e}")
            continue
    
    # Выводим статистику
    print("\n" + "="*50)
    print("📈 СТАТИСТИКА МИГРАЦИИ:")
    print(f"➕ Добавлено новых переводов: {added_count}")
    print(f"🔄 Обновлено существующих: {updated_count}")
    print(f"⏭️ Пропущено (без изменений): {skipped_count}")
    print(f"📊 Всего обработано ключей: {len(all_keys)}")
    print("="*50)
    
    return True

def verify_migration():
    """Проверяет корректность миграции"""
    try:
        db = PostgreSQLConnector(
            database='journal', 
            user='postgres', 
            password='1', 
            host='127.0.0.1', 
            port='5432'
        )
        
        # Получаем все переводы из БД
        translations = db.translations.all().exec()
        
        print(f"\n🔍 ПРОВЕРКА МИГРАЦИИ:")
        print(f"📊 В БД найдено {len(translations)} переводов")
        
        # Проверяем несколько ключевых переводов
        test_keys = ['website_title', 'login', 'register', 'my_articles', 'submit_article']
        
        for key in test_keys:
            trans = next((t for t in translations if t['alias'] == key), None)
            if trans:
                print(f"✅ {key}: EN='{trans['content'][:30]}...', RU='{trans['content_ru'][:30]}...', UZ='{trans['content_uz'][:30]}...'")
            else:
                print(f"❌ {key}: НЕ НАЙДЕН")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке: {e}")
        return False

if __name__ == "__main__":
    print("🚀 МИГРАЦИЯ ПЕРЕВОДОВ В БАЗУ ДАННЫХ")
    print("="*50)
    
    if migrate_translations():
        print("\n✅ Миграция завершена успешно!")
        verify_migration()
    else:
        print("\n❌ Миграция завершилась с ошибками!")
        sys.exit(1)
