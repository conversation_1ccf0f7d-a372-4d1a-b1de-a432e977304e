#!/usr/bin/env python3
"""
Модуль для единой авторизации между fmadmin и mainweb через токены
"""

import secrets
import time
import hashlib
import sys
import os

# Добавляем пути к модулям
sys.path.append(os.path.join(os.path.dirname(__file__), 'fmadmin'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'mainweb'))

from fmadmin.connector import PostgreSQLConnector

class SharedAuthManager:
    """Менеджер единой авторизации через токены"""
    
    def __init__(self, db_config=None):
        """
        Инициализация менеджера авторизации
        
        Args:
            db_config: Конфигурация БД (по умолчанию используется стандартная)
        """
        if db_config is None:
            db_config = {
                'database': 'journal',
                'user': 'postgres', 
                'password': '1',
                'host': '127.0.0.1',
                'port': '5432'
            }
        
        self.db = PostgreSQLConnector(**db_config)
        self.token_ttl = 30 * 24 * 60 * 60  # 30 дней в секундах
    
    def generate_auth_token(self, user_id):
        """
        Генерирует новый токен авторизации для пользователя
        
        Args:
            user_id: ID пользователя
            
        Returns:
            str: Сгенерированный токен
        """
        # Генерируем случайный токен
        token = secrets.token_urlsafe(32)
        
        # Добавляем timestamp для проверки срока действия
        timestamp = int(time.time())
        token_with_timestamp = f"{token}:{timestamp}"
        
        # Хешируем токен для безопасного хранения
        token_hash = hashlib.sha256(token_with_timestamp.encode()).hexdigest()
        
        # Обновляем токен в БД
        self.db.users.all().equal(id=user_id).update(
            token=token_hash,
            last_online=timestamp
        ).exec()
        
        return token_with_timestamp
    
    def validate_token(self, token):
        """
        Проверяет валидность токена и возвращает пользователя
        
        Args:
            token: Токен для проверки
            
        Returns:
            dict|None: Данные пользователя или None если токен невалиден
        """
        if not token or ':' not in token:
            return None
        
        try:
            # Хешируем переданный токен
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            
            # Ищем пользователя с таким токеном
            users = self.db.users.all().equal(token=token_hash).exec()
            if not users:
                return None
            
            user = users[0]
            
            # Проверяем, не заблокирован ли пользователь
            if user.get('is_blocked'):
                return None
            
            # Извлекаем timestamp из токена
            token_part, timestamp_str = token.split(':', 1)
            timestamp = int(timestamp_str)
            
            # Проверяем срок действия токена
            current_time = int(time.time())
            if current_time - timestamp > self.token_ttl:
                # Токен истек, удаляем его
                self.invalidate_token(user['id'])
                return None
            
            # Обновляем время последней активности
            self.db.users.all().equal(id=user['id']).update(
                last_online=current_time
            ).exec()
            
            return user
            
        except (ValueError, TypeError):
            return None
    
    def invalidate_token(self, user_id):
        """
        Аннулирует токен пользователя (выход из системы)
        
        Args:
            user_id: ID пользователя
        """
        self.db.users.all().equal(id=user_id).update(
            token=None
        ).exec()
    
    def login_user(self, email, password):
        """
        Авторизует пользователя и генерирует токен
        
        Args:
            email: Email пользователя
            password: Пароль пользователя
            
        Returns:
            tuple: (user_data, token) или (None, None) при ошибке
        """
        # Ищем пользователя по email
        users = self.db.users.all().equal(email=email).exec()
        if not users:
            return None, None
        
        user = users[0]
        
        # Проверяем пароль (в будущем нужно заменить на хеширование)
        if user.get('password') != password:
            return None, None
        
        # Проверяем, не заблокирован ли пользователь
        if user.get('is_blocked'):
            return None, None
        
        # Генерируем токен
        token = self.generate_auth_token(user['id'])
        
        return user, token
    
    def get_user_by_id(self, user_id):
        """
        Получает данные пользователя по ID
        
        Args:
            user_id: ID пользователя
            
        Returns:
            dict|None: Данные пользователя или None
        """
        users = self.db.users.all().equal(id=user_id).exec()
        return users[0] if users else None
    
    def check_admin_access(self, user):
        """
        Проверяет права администратора
        
        Args:
            user: Данные пользователя
            
        Returns:
            bool: True если пользователь админ или редактор
        """
        if not user:
            return False
        return user.get('rolename') in ['admin', 'editor']
    
    def check_user_access(self, user):
        """
        Проверяет права обычного пользователя
        
        Args:
            user: Данные пользователя
            
        Returns:
            bool: True если пользователь авторизован
        """
        return user is not None and not user.get('is_blocked', False)

# Глобальный экземпляр менеджера авторизации
auth_manager = SharedAuthManager()

def get_auth_manager():
    """Возвращает глобальный экземпляр менеджера авторизации"""
    return auth_manager

# Вспомогательные функции для быстрого использования
def login_user(email, password):
    """Быстрая авторизация пользователя"""
    return auth_manager.login_user(email, password)

def validate_token(token):
    """Быстрая проверка токена"""
    return auth_manager.validate_token(token)

def invalidate_token(user_id):
    """Быстрый выход из системы"""
    return auth_manager.invalidate_token(user_id)

def generate_auth_token(user_id):
    """Быстрая генерация токена"""
    return auth_manager.generate_auth_token(user_id)

if __name__ == "__main__":
    # Тестирование модуля
    print("🧪 ТЕСТИРОВАНИЕ МОДУЛЯ ЕДИНОЙ АВТОРИЗАЦИИ")
    print("="*50)
    
    # Тест подключения к БД
    try:
        users = auth_manager.db.users.all().limit(1).exec()
        print(f"✅ Подключение к БД: OK (найдено пользователей: {len(users)})")
    except Exception as e:
        print(f"❌ Ошибка подключения к БД: {e}")
        exit(1)
    
    print("\n✅ Модуль готов к использованию!")
