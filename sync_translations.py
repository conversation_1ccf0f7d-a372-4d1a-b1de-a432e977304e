#!/usr/bin/env python3
"""
Скрипт для синхронизации переводов между fmadmin и mainweb
Обновляет кеш переводов в mainweb после изменений в fmadmin
"""

import requests
import sys
import os

# Добавляем пути к модулям
sys.path.append(os.path.join(os.path.dirname(__file__), 'fmadmin'))

from fmadmin.connector import PostgreSQLConnector

def sync_translations_to_mainweb(mainweb_url="http://localhost:16534"):
    """
    Синхронизирует переводы с mainweb через API
    
    Args:
        mainweb_url: URL основного сайта
    """
    
    print("🔄 СИНХРОНИЗАЦИЯ ПЕРЕВОДОВ")
    print("="*50)
    
    try:
        # Подключение к БД для проверки актуальности
        db = PostgreSQLConnector(
            database='journal', 
            user='postgres', 
            password='1', 
            host='127.0.0.1', 
            port='5432'
        )
        
        # Получаем количество переводов в БД
        translations = db.translations.all().exec()
        print(f"📊 В БД найдено {len(translations)} переводов")
        
        # Очищаем кеш переводов в mainweb
        api_url = f"{mainweb_url}/api/translations/clear_cache"
        
        print(f"🌐 Отправляем запрос на очистку кеша: {api_url}")
        
        response = requests.post(api_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Кеш переводов в mainweb успешно очищен")
                print("🔄 Переводы будут автоматически загружены при следующем запросе")
                return True
            else:
                print(f"❌ Ошибка API: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP ошибка: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Не удается подключиться к {mainweb_url}")
        print("💡 Убедитесь, что mainweb запущен и доступен")
        return False
    except requests.exceptions.Timeout:
        print("❌ Таймаут при подключении к mainweb")
        return False
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False

def check_translations_consistency():
    """Проверяет консистентность переводов в БД"""
    
    print("\n🔍 ПРОВЕРКА КОНСИСТЕНТНОСТИ ПЕРЕВОДОВ")
    print("="*50)
    
    try:
        db = PostgreSQLConnector(
            database='journal', 
            user='postgres', 
            password='1', 
            host='127.0.0.1', 
            port='5432'
        )
        
        translations = db.translations.all().exec()
        
        # Статистика
        total_count = len(translations)
        empty_en_count = 0
        empty_ru_count = 0
        empty_uz_count = 0
        
        for trans in translations:
            if not trans.get('content', '').strip():
                empty_en_count += 1
            if not trans.get('content_ru', '').strip():
                empty_ru_count += 1
            if not trans.get('content_uz', '').strip():
                empty_uz_count += 1
        
        print(f"📊 Всего переводов: {total_count}")
        print(f"🇺🇸 Пустых EN переводов: {empty_en_count}")
        print(f"🇷🇺 Пустых RU переводов: {empty_ru_count}")
        print(f"🇺🇿 Пустых UZ переводов: {empty_uz_count}")
        
        # Проверяем ключевые переводы
        key_translations = [
            'website_title', 'login', 'register', 'my_articles', 
            'submit_article', 'logout', 'home', 'about', 'contact'
        ]
        
        print(f"\n🔑 Проверка ключевых переводов:")
        missing_keys = []
        
        for key in key_translations:
            trans = next((t for t in translations if t['alias'] == key), None)
            if trans:
                print(f"✅ {key}")
            else:
                print(f"❌ {key} - ОТСУТСТВУЕТ")
                missing_keys.append(key)
        
        if missing_keys:
            print(f"\n⚠️ Отсутствуют ключевые переводы: {', '.join(missing_keys)}")
            return False
        else:
            print(f"\n✅ Все ключевые переводы присутствуют")
            return True
            
    except Exception as e:
        print(f"❌ Ошибка при проверке: {e}")
        return False

def main():
    """Основная функция"""
    
    if len(sys.argv) > 1:
        mainweb_url = sys.argv[1]
    else:
        mainweb_url = "http://localhost:16534"
    
    print(f"🎯 Целевой URL mainweb: {mainweb_url}")
    
    # Проверяем консистентность переводов
    if not check_translations_consistency():
        print("\n⚠️ Обнаружены проблемы с переводами в БД")
        print("💡 Рекомендуется запустить migrate_translations.py")
    
    # Синхронизируем с mainweb
    if sync_translations_to_mainweb(mainweb_url):
        print("\n✅ Синхронизация завершена успешно!")
    else:
        print("\n❌ Синхронизация завершилась с ошибками!")
        sys.exit(1)

if __name__ == "__main__":
    main()
